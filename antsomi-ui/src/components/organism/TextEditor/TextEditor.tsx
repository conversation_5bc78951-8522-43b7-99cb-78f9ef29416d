import { ANTSOMI_COMPONENT_PREFIX_CLS } from '@antscorp/antsomi-ui/es/constants';
import { useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';
import SubScript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import TextAlign from '@tiptap/extension-text-align';
import { TextStyleKit } from '@tiptap/extension-text-style';
import { Selection } from '@tiptap/extensions';
import { EditorState } from '@tiptap/pm/state';
import { EditorView } from '@tiptap/pm/view';
import { Editor, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import clsx from 'clsx';
import produce from 'immer';
import { isBoolean, omit } from 'lodash';
import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { DEFAULT_TEXT_STYLE } from './constants';
import { BackgroundColor } from './extensions/BackgroundColor';
import { Color } from './extensions/Color';
import { Emoji } from './extensions/Emoji';
import { FontFamily } from './extensions/FontFamily';
import { FontSize } from './extensions/FontSize';
import { FontWeight } from './extensions/FontWeight';
import { Indent } from './extensions/Indent';
import { LetterSpacing } from './extensions/LetterSpacing';
import { LineHeight } from './extensions/LineHeight';
import { CustomLink } from './extensions/Link';
import { CustomOrderedList } from './extensions/OrderedList';
import { SmartTag } from './extensions/SmartTag';
import { TextTransform } from './extensions/TextTransform';
import { CustomUnorderedList } from './extensions/UnorderedList';
import { ClearFormatting } from './extensions/ClearFormatting';
import { useTextEditorStore } from './provider';
import { StyledBubbleMenu, StyledEditorContent } from './styled';
import { TextEditorProps, TextEditorRef, TextStyle } from './types';
import { emojiSuggestion } from './ui/Emoji';
import { Toolbar } from './ui/Toolbar';
import {
  defaultShouldShowBubbleMenu,
  handleLinkAction,
  handleSmartTagAction,
  isDynamicLink,
  isShowLinkToolbar,
  safeParseHTMLContent,
} from './utils';

export const TextEditor = memo(
  forwardRef<TextEditorRef, TextEditorProps>((props, ref) => {
    const {
      id,
      className,
      config,
      editable = true,
      initialContent,
      dataAttributes,
      onUpdateDebounced = 400,
      defaultTextStyle: defaultTextStyleProp,
      linkHandler: outerLinkHandler,
      smartTagHandler: outerSmartTagHandler,
      bubbleMenuProps,
      style,
      render,
      onUpdate,
      onFocus,
      onCreate,
    } = props;

    const isShowBubbleMenu = useTextEditorStore(state => state.isShowBubbleMenu);
    const setBubbleMenuContainer = useTextEditorStore(state => state.setBubbleMenuContainer);

    const defaultTextStyle: TextStyle = useDeepCompareMemo(
      () => ({
        ...DEFAULT_TEXT_STYLE,
        ...defaultTextStyleProp,
      }),
      [defaultTextStyleProp],
    );

    const handleOnUpdateDebounce = useDebouncedCallback((editor: Editor) => {
      const html = editor.getHTML();
      const text = editor.getText();
      const json = editor.getJSON();

      onUpdate?.({ html, text, json });
    }, onUpdateDebounced);

    const contentRef = useRef<HTMLDivElement | null>(null);

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          link: false,
          bulletList: false,
          orderedList: false,
          listItem: {
            HTMLAttributes: {
              style: 'line-height:normal;',
            },
          },
          undoRedo: {
            depth: 100,
          },
        }),

        CustomUnorderedList,
        CustomOrderedList,

        FontWeight.configure({
          defaultWeight: defaultTextStyle.fontWeight,
        }),
        LineHeight.configure({
          defaultHeight: defaultTextStyle.lineHeight,
        }),
        FontFamily.configure({
          defaultFontFamily: defaultTextStyle.fontFamily,
        }),
        FontSize.configure({
          defaultFontSize: defaultTextStyle.fontSize,
        }),
        LetterSpacing.configure({
          defaultLetterSpacing: defaultTextStyle.letterSpacing,
        }),
        Color.configure({
          defaultColor: defaultTextStyle.color,
        }),
        BackgroundColor.configure({
          defaultBackgroundColor: defaultTextStyle.backgroundColor,
        }),
        TextStyleKit.configure({
          backgroundColor: false,
          color: false,
          textStyle: {
            mergeNestedSpanStyles: true,
          },
        }),
        TextTransform,
        Superscript,
        SubScript,
        Selection.configure({
          className: 'selection',
        }),
        SmartTag.configure({
          highlight: config?.SmartTag?.highlight,
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        CustomLink.configure({
          openOnClick: false,
          HTMLAttributes: {
            class: 'link',
            style: 'text-decoration:inherit;color:inherit;',
            rel: undefined,
          },
          highlight: config?.Link?.highlightDynamic,
        }),
        Indent,
        Emoji.configure({
          suggestion: emojiSuggestion({
            container: bubbleMenuProps?.container,
          }),
        }),
        ClearFormatting.configure({
          defaultTextStyle,
        }),
      ],
      content: safeParseHTMLContent(initialContent || ''),
      onCreate: ({ editor }) => {
        const html = editor.getHTML();
        const text = editor.getText();
        const json = editor.getJSON();

        onCreate?.({ html, text, json });
      },
      onUpdate: ({ editor }) => {
        handleOnUpdateDebounce(editor);
      },
      onFocus: ({ event }) => {
        onFocus?.(event);
      },
      editorProps: {
        attributes: { spellcheck: 'false' },
        handleKeyDown(view: EditorView, event) {
          if (event.ctrlKey && event.key === 'k') {
            // Add this line to prevent browser's default behavior.
            event.preventDefault();

            handleLinkAction(view, outerLinkHandler);
          }
        },
      },
    });

    useEffect(() => {
      if (editable === undefined) {
        editor?.setEditable(true);
      } else if (isBoolean(editable)) {
        editor?.setEditable(editable);
      }
    }, [editor, editable]);

    useEffect(() => {
      if (editor && config?.SmartTag?.highlight !== undefined) {
        editor.chain().updateSmartTagHighlight(config.SmartTag.highlight).run();
      }
    }, [editor, config?.SmartTag?.highlight]);

    useEffect(() => {
      if (!isBoolean(config?.Link?.highlightDynamic)) {
        return;
      }

      editor
        .chain()
        .updateCustomLinkAttrsGlobally(
          currentAttrs => isDynamicLink(currentAttrs),
          ({ currentAttrs }) => ({
            attrs: produce(currentAttrs, draft => {
              const updatedClass = new Set(String(currentAttrs.class).split(' '));

              if (config?.Link?.highlightDynamic) {
                updatedClass.add('highlight');
              } else {
                updatedClass.delete('highlight');
              }

              draft.class = [...updatedClass].join(' ');
            }),
          }),
        )
        .run();
    }, [config?.Link?.highlightDynamic, editor]);

    const handleSetSmartTag = useCallback<TextEditorRef['setSmartTag']>(
      ({ id, content }) => {
        editor?.chain().focus().setSmartTag({ id, content }).run();
      },
      [editor],
    );

    const handleDeleteSmartTag = useCallback<TextEditorRef['deleteSmartTag']>(
      id => {
        editor
          ?.chain()
          .deleteSmartTagGlobally(attrs => attrs.id === id)
          .run();
      },
      [editor],
    );

    const handleUpdateSmartTagAttrs = useCallback<TextEditorRef['updateSmartTagAttrs']>(
      (id, updatedAttrs) =>
        editor
          ?.chain()
          .updateSmartTagAttrsGlobally(attrs => {
            if (attrs.id === id) return updatedAttrs;
          })
          .run(),
      [editor],
    );

    const shouldShowBubbleMenu = useCallback(
      (params: { element: HTMLElement; state: EditorState; view: EditorView; editor: Editor }) => {
        const { state, view, element } = params;

        if (isShowLinkToolbar(state)) {
          return true;
        }

        return defaultShouldShowBubbleMenu({
          state,
          view,
          element,
          editor: params.editor,
        });
      },
      [],
    );

    const handleBlur = useCallback(
      (perserveSelection?: boolean) => {
        if (perserveSelection) {
          editor?.chain().blur().run();
        } else {
          editor?.chain().blur().setTextSelection({ from: 0, to: 0 }).run();
        }
      },
      [editor],
    );

    const handleBubbleMenuRef = useCallback(
      (htmlElement: HTMLDivElement | null) => {
        setBubbleMenuContainer(htmlElement);
      },
      [setBubbleMenuContainer],
    );

    useImperativeHandle(ref, () => ({
      setLink: params => {
        editor?.chain().setCustomLink(params).run();
      },
      deleteLink: predicate => {
        editor?.chain().deleteCustomLink(predicate).run();
      },
      updateLinkAttrs: (predicate, updateFn) => {
        editor.chain().updateCustomLinkAttrsGlobally(predicate, updateFn).run();
      },
      setSmartTag: handleSetSmartTag,
      deleteSmartTag: handleDeleteSmartTag,
      updateSmartTagAttrs: handleUpdateSmartTagAttrs,
      blur: handleBlur,
      style: contentRef.current?.style,
      editor,
    }));

    if (!editor) {
      return null;
    }

    return (
      <>
        <StyledEditorContent
          className={clsx(`${ANTSOMI_COMPONENT_PREFIX_CLS}-text-editor`, className)}
          $textStyle={defaultTextStyle}
          style={{
            // Inline styles apply to inner html elements (support email client)
            ...omit(defaultTextStyle, ['fontSize']),
            ...style,
          }}
          id={id}
          ref={contentRef}
          editor={editor}
          {...dataAttributes}
        />

        <StyledBubbleMenu
          ref={handleBubbleMenuRef}
          editor={editor}
          shouldShow={shouldShowBubbleMenu}
          resizeDelay={700}
          {...bubbleMenuProps}
        >
          {isShowBubbleMenu && (
            <Toolbar
              config={config}
              editor={editor}
              defaultTextStyle={defaultTextStyle}
              linkHanlder={{
                onUpsert: () => {
                  handleLinkAction(editor.view, outerLinkHandler);
                },
              }}
              smartTagHandler={{
                onUpsert: (event: React.MouseEvent) => {
                  handleSmartTagAction(event, editor.view, outerSmartTagHandler);
                },
              }}
              render={render}
            />
          )}
        </StyledBubbleMenu>
      </>
    );
  }),
);
