import { JSONContent } from '@tiptap/core';
import { TextEditor as TextEditorComponent } from './TextEditor';
import { htmlMinifyForEmail } from './utils';

export type {
  TextEditorProps,
  TextEditorRef,
  TextEditorComponentsRender,
  LinkAttrs,
} from './types';

export { isLinkMark, isLinkMarkRange } from './types';

export { CUSTOM_LINK_EXTENSION_NAME } from './constants';

export type { JSONContent as TextEditorJSONContent };

export {
  TextEditorProvider,
  type TextEditorProviderProps,
  type TextEditorProviderRefHandler,
} from './provider';

export const TextEditor = Object.assign(TextEditorComponent, {
  Utils: { htmlMinifyForEmail },
});
