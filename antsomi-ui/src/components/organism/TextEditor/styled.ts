import { EditorContent } from '@tiptap/react';
import styled, { css } from 'styled-components';
import { BubbleMenu } from './ui/BubbleMenu';
import { SMART_TAG_HIGHLIGHT_BG } from './constants';
import { Flex } from 'antd';
import { TextStyle } from './types';
import tinycolor from 'tinycolor2';

export const StyledBubbleMenu = styled(BubbleMenu)`
  background-color: white;
  border-radius: 5px;
  padding: 8px;
  box-shadow: var(--antsomi-box-shadow-secondary);
`;

const dynContentStyles = css`
  background-color: ${SMART_TAG_HIGHLIGHT_BG};
  background-color: color-mix(
    in lab,
    var(--text-style-bg-color, transparent),
    ${SMART_TAG_HIGHLIGHT_BG}
  );

  &:hover {
    background-color: ${SMART_TAG_HIGHLIGHT_BG};
    background-color: color-mix(
      in lab,
      var(--text-style-bg-color, transparent),
      ${SMART_TAG_HIGHLIGHT_BG} 80%
    );
  }
`;

export const StyledEditorContent = styled(EditorContent)<{
  $textStyle: TextStyle;
}>`
  .tiptap {
    outline: none;

    p {
      margin: 0;
      padding: 0;
    }

    &:not([contenteditable='false']) {
      p {
        cursor: text;
      }

      .selection {
        background-color: #d7cac9;
        background-color: color-mix(in lab, var(--text-style-bg-color, transparent), #d7cac9);
      }

      .smart-tag {
        cursor: pointer;

        &.highlight {
          ${dynContentStyles}
        }
      }

      .link {
        &.highlight > span {
          ${dynContentStyles}
        }
      }
    }
  }
`;

export const ColorBox = styled.div<{ $color?: string }>`
  cursor: pointer;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  transition: box-shadow 0.3s ease-in-out;
  border: 1px solid #d9d9d9;
  background-color: ${p => p.$color};

  &:hover {
    box-shadow: 0 0 5px ${p => tinycolor(p.$color).setAlpha(0.45).toRgbString()};
    cursor: pointer;
  }
`;

export const ToolbarWrapper = styled(Flex)`
  .antsomi-divider.toolbar-separator {
    height: 28px;
    margin: 2px;
  }
`;
