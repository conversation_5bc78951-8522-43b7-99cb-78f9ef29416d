import { SMART_TAG_HIGHLIGHT_BG } from '../constants';
import { textStyleFromElStyle, appendTextStyleToEl } from './style';
import tinycolor from 'tinycolor2';

/**
 * Creates a clean span element for a dynamic tag with only the essential attributes
 *
 * @param doc - The document used to create the new element
 * @param originalSpan - The original dynamic tag span element
 * @param attributesToPreserve - List of attribute names to copy from the original span
 * @returns A new span element with preserved attributes
 */
function createCleanDynamicSpan(
  doc: Document,
  originalSpan: HTMLSpanElement,
  attributesToPreserve: string[],
): HTMLSpanElement {
  const cleanSpan = doc.createElement('span');

  // Preserve only the specified attributes
  for (const attrName of attributesToPreserve) {
    if (originalSpan.hasAttribute(attrName)) {
      cleanSpan.setAttribute(attrName, originalSpan.getAttribute(attrName)!);
    }
  }

  // Preserve the text content
  cleanSpan.textContent = originalSpan.textContent?.trim() || '';

  return cleanSpan;
}

/**
 * Extracts style wrapper elements that need to be preserved from the original dynamic tag
 *
 * @param doc - The document containing the dynamic tag
 * @param html - The original HTML string
 * @param dynamicSpan - The original dynamic tag span element
 * @returns Array of wrapper elements that provide styling
 */
function extractStyleWrappers(
  doc: Document,
  html: string,
  dynamicSpan: HTMLSpanElement,
): Element[] {
  const styleWrappers: Element[] = [];

  // Create a temporary container for style computation
  const tempStyleContainer = document.createElement('div');
  tempStyleContainer.style.display = 'none';
  document.body.appendChild(tempStyleContainer);

  try {
    // Extract computed styles from the original element
    if (doc.body.firstElementChild) {
      const tempDoc = new DOMParser().parseFromString(html, 'text/html');

      if (tempDoc.body.firstElementChild) {
        tempStyleContainer.appendChild(tempDoc.body.firstElementChild);

        const originalDynamicElement = tempStyleContainer.querySelector(
          `span[data-dynamic-id="${dynamicSpan.dataset.dynamicId}"]`,
        );

        if (originalDynamicElement) {
          // Create a span with the computed text styles
          const computedTextStyle = textStyleFromElStyle(
            window.getComputedStyle(originalDynamicElement),
          );

          // Remove background color if it matches the highlight color
          if (tinycolor.equals(computedTextStyle.backgroundColor, SMART_TAG_HIGHLIGHT_BG)) {
            computedTextStyle.backgroundColor = '';
          }

          const textStyleWrapper = document.createElement('span');
          appendTextStyleToEl(textStyleWrapper, computedTextStyle);
          styleWrappers.push(textStyleWrapper);
        }
      }
    }

    // Extract nested style wrappers
    let nestedElement = dynamicSpan.firstElementChild;
    while (nestedElement && nestedElement.children.length <= 1) {
      styleWrappers.push(nestedElement);
      nestedElement = nestedElement.firstElementChild || null;
    }

    return styleWrappers;
  } finally {
    // Clean up the temporary container
    document.body.removeChild(tempStyleContainer);
  }
}

/**
 * Reconstructs the dynamic tag element hierarchy with preserved style wrappers
 *
 * @param doc - The document used to create new elements
 * @param baseSpan - The clean base span element for the dynamic tag
 * @param styleWrappers - Array of style wrapper elements to apply
 * @returns The reconstructed dynamic tag with preserved styling
 */
function reconstructDynamicTagHierarchy(
  doc: Document,
  baseSpan: HTMLSpanElement,
  styleWrappers: Element[],
): Node {
  let resultNode: Node = baseSpan;

  // Apply style wrappers from innermost to outermost
  styleWrappers.reverse().forEach(originalWrapper => {
    const wrapperClone = doc.createElement(originalWrapper.tagName.toLowerCase());

    // Clone all attributes from the original wrapper
    for (let i = 0; i < originalWrapper.attributes.length; i++) {
      const { name, value } = originalWrapper.attributes[i];
      wrapperClone.setAttribute(name, value);
    }

    wrapperClone.appendChild(resultNode);
    resultNode = wrapperClone;
  });

  return resultNode;
}

/**
 * Restructures dynamic tags from Froala editor format to be compatible with Tiptap editor.
 * This function preserves styling and attributes of dynamic tags while cleaning up their structure.
 *
 * @param html - The HTML string containing dynamic tags
 * @param options - Configuration options for the restructuring process
 * @returns The restructured HTML string
 */
export function restructureDynamicTagsWithOptions(
  html: string,
  options: {
    attributesToClone: string[];
  } = {
    attributesToClone: [
      'class',
      'id',
      'style',
      'data-dynamic',
      'data-dynamic-id',
      'direction',
      'unicode-bidi',
      'text-align',
      'background-color',
      'color',
    ],
  },
) {
  // Parse the HTML string into a DOM
  const parser = new DOMParser();
  const parsedDoc = parser.parseFromString(html, 'text/html');

  // Find all dynamic tag spans
  const dynamicTagSpans = parsedDoc.querySelectorAll<HTMLSpanElement>('span[data-dynamic]');

  // Process each dynamic span
  dynamicTagSpans.forEach(originalDynamicSpan => {
    // Step 1: Create a new clean span with essential attributes
    const cleanDynamicSpan = createCleanDynamicSpan(
      parsedDoc,
      originalDynamicSpan,
      options.attributesToClone,
    );

    // Step 2: Extract style wrappers that need to be preserved
    const styleWrappers = extractStyleWrappers(parsedDoc, html, originalDynamicSpan);

    // Step 3: Reconstruct the element hierarchy with preserved styles
    const restructuredDynamicTag = reconstructDynamicTagHierarchy(
      parsedDoc,
      cleanDynamicSpan,
      styleWrappers,
    );

    // Step 4: Replace the original span with the restructured node
    originalDynamicSpan.replaceWith(restructuredDynamicTag);
  });

  return parsedDoc.body.innerHTML;
}

/**
 * Converts inherited text styles to computed styles for span elements in HTML content.
 * This function processes span elements to ensure text styles are explicitly set rather than inherited.
 *
 * @param html - The HTML string to process
 * @returns The processed HTML string with computed styles applied
 */
const inheritStyleToComputedStyle = (html: string): string => {
  try {
    // Create a new DOM parser and parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    if (!doc.body) {
      // eslint-disable-next-line no-console
      console.warn('Failed to parse HTML: body not found');
      return html;
    }

    // Create a hidden container
    const container = document.createElement('div');

    container.style.visibility = 'hidden';
    container.innerHTML = doc.body.innerHTML;

    // Add container to DOM temporarily to compute styles
    document.body.appendChild(container);

    // Process all span elements
    const spanElements = container.querySelectorAll<HTMLSpanElement>('span');

    spanElements.forEach(span => {
      const textStyle = textStyleFromElStyle(span.style);
      const computedStyle = window.getComputedStyle(span);

      // Only update styles if there are inherited values
      const shouldUpdateStyle = Object.values(textStyle).some(value => value === 'inherit');

      if (!shouldUpdateStyle) {
        return;
      }

      // Update inherited styles with computed values
      Object.entries(textStyle).forEach(([key, value]) => {
        if (value === 'inherit' && key in span.style) {
          span.style[key] = computedStyle[key];
        }
      });
    });

    const resultHTML = container.innerHTML;

    // Clean up
    container.remove();

    return resultHTML;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error processing styles:', error);
    return html; // Return original HTML in case of error
  }
};

/**
 * Safely parses and processes HTML content for editor use
 * @param html - HTML string to process
 * @returns Processed HTML string
 */
export function safeParseHTMLContent(html: string): string {
  let resultHTML = html;

  const isFroala = html.includes('fr-box');

  if (isFroala) {
    console.log('safeParseHTMLContent', html);

    resultHTML = inheritStyleToComputedStyle(html);

    console.log('after inheritStyleToComputedStyle', resultHTML);

    resultHTML = restructureDynamicTagsWithOptions(resultHTML);

    // console.log('after restructureDynamicTagsWithOptions', resultHTML);
  }

  return resultHTML;
}

/**
 * Sanitizes HTML by removing potentially dangerous elements and attributes
 * @param html - HTML string to sanitize
 * @returns Sanitized HTML string
 */
export function sanitizeHTML(html: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // Remove script tags
  const scripts = doc.querySelectorAll('script');
  scripts.forEach(script => script.remove());

  // Remove on* event attributes
  const allElements = doc.querySelectorAll('*');
  allElements.forEach(element => {
    Array.from(element.attributes).forEach(attr => {
      if (attr.name.startsWith('on')) {
        element.removeAttribute(attr.name);
      }
    });
  });

  return doc.body.innerHTML;
}

/**
 * Extracts text content from HTML
 * @param html - HTML string
 * @returns Plain text content
 */
export function extractTextFromHTML(html: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  return doc.body.textContent || '';
}

/**
 * Counts words in HTML content
 * @param html - HTML string
 * @returns Word count
 */
export function countWordsInHTML(html: string): number {
  const text = extractTextFromHTML(html);
  return text
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0).length;
}

/**
 * Counts characters in HTML content
 * @param html - HTML string
 * @param includeSpaces - Whether to include spaces in count
 * @returns Character count
 */
export function countCharactersInHTML(html: string, includeSpaces = true): number {
  const text = extractTextFromHTML(html);
  return includeSpaces ? text.length : text.replace(/\s/g, '').length;
}

/**
 * Wraps text nodes with specified tag
 * @param html - HTML string
 * @param tagName - Tag name to wrap with
 * @param className - Optional class name for wrapper
 * @returns HTML with wrapped text nodes
 */
export function wrapTextNodes(html: string, tagName = 'span', className?: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  function wrapTextNodesRecursive(node: Node) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
      const wrapper = doc.createElement(tagName);
      if (className) {
        wrapper.className = className;
      }
      wrapper.textContent = node.textContent;
      node.parentNode?.replaceChild(wrapper, node);
    } else {
      Array.from(node.childNodes).forEach(wrapTextNodesRecursive);
    }
  }

  wrapTextNodesRecursive(doc.body);
  return doc.body.innerHTML;
}

/**
 * Removes empty elements from HTML
 * @param html - HTML string
 * @returns HTML with empty elements removed
 */
export function removeEmptyElements(html: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  function removeEmptyRecursive(node: Element) {
    Array.from(node.children).forEach(child => {
      removeEmptyRecursive(child as Element);

      if (!child.textContent?.trim() && child.children.length === 0) {
        child.remove();
      }
    });
  }

  removeEmptyRecursive(doc.body);
  return doc.body.innerHTML;
}

/**
 * Normalizes whitespace in HTML
 * @param html - HTML string
 * @returns HTML with normalized whitespace
 */
export function normalizeWhitespace(html: string): string {
  return html.replace(/\s+/g, ' ').replace(/>\s+</g, '><').trim();
}

export const htmlMinifyForEmail = (htmlEditorContent: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlEditorContent, 'text/html');
  const allElements = doc.body.querySelectorAll('*');

  allElements.forEach(element => {
    element.removeAttribute('spellcheck');
    element.removeAttribute('class');
    element.removeAttribute('contenteditable');
  });

  return doc.body.innerHTML;
};
